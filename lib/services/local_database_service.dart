import 'package:flutter/foundation.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:tolk/models/local_models.dart';
import 'package:tolk/models/chat_models.dart';

class LocalDatabaseService {
  static final LocalDatabaseService _instance = LocalDatabaseService._internal();
  factory LocalDatabaseService() => _instance;
  LocalDatabaseService._internal();

  // Box names
  static const String _messagesBoxName = 'messages';
  static const String _chatRoomsBoxName = 'chatRooms';
  static const String _usersBoxName = 'users';
  static const String _syncStatusBoxName = 'syncStatus';

  // Boxes
  Box<LocalMessage>? _messagesBox;
  Box<LocalChatRoom>? _chatRoomsBox;
  Box<LocalUser>? _usersBox;
  Box<SyncStatus>? _syncStatusBox;

  bool _isInitialized = false;

  // Initialize the local database
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize Hive
      await Hive.initFlutter();

      // Register adapters
      if (!Hive.isAdapterRegistered(0)) {
        Hive.registerAdapter(LocalMessageAdapter());
      }
      if (!Hive.isAdapterRegistered(1)) {
        Hive.registerAdapter(LocalChatRoomAdapter());
      }
      if (!Hive.isAdapterRegistered(2)) {
        Hive.registerAdapter(LocalUserAdapter());
      }
      if (!Hive.isAdapterRegistered(3)) {
        Hive.registerAdapter(SyncStatusAdapter());
      }

      // Open boxes
      _messagesBox = await Hive.openBox<LocalMessage>(_messagesBoxName);
      _chatRoomsBox = await Hive.openBox<LocalChatRoom>(_chatRoomsBoxName);
      _usersBox = await Hive.openBox<LocalUser>(_usersBoxName);
      _syncStatusBox = await Hive.openBox<SyncStatus>(_syncStatusBoxName);

      _isInitialized = true;
      debugPrint('💾 [LOCAL_DB] Local database initialized successfully');
    } catch (e) {
      debugPrint('❌ [LOCAL_DB] Error initializing local database: $e');
      rethrow;
    }
  }

  // Ensure initialization
  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  // === MESSAGE OPERATIONS ===

  // Save message locally
  Future<void> saveMessage(LocalMessage message) async {
    await _ensureInitialized();
    try {
      await _messagesBox!.put(message.id, message);
      debugPrint('💾 [LOCAL_DB] Message saved: ${message.id}');
    } catch (e) {
      debugPrint('❌ [LOCAL_DB] Error saving message: $e');
      rethrow;
    }
  }

  // Get messages for a chat room
  List<LocalMessage> getMessagesForChatRoom(String chatRoomId) {
    if (!_isInitialized || _messagesBox == null) return [];
    
    try {
      final messages = _messagesBox!.values
          .where((message) => message.chatRoomId == chatRoomId)
          .toList();
      
      // Sort by timestamp (newest first)
      messages.sort((a, b) => b.timestamp.compareTo(a.timestamp));
      
      debugPrint('💾 [LOCAL_DB] Retrieved ${messages.length} messages for chat room: $chatRoomId');
      return messages;
    } catch (e) {
      debugPrint('❌ [LOCAL_DB] Error getting messages for chat room: $e');
      return [];
    }
  }

  // Get all unsynced messages
  List<LocalMessage> getUnsyncedMessages() {
    if (!_isInitialized || _messagesBox == null) return [];
    
    try {
      final unsyncedMessages = _messagesBox!.values
          .where((message) => !message.isSynced)
          .toList();
      
      debugPrint('💾 [LOCAL_DB] Found ${unsyncedMessages.length} unsynced messages');
      return unsyncedMessages;
    } catch (e) {
      debugPrint('❌ [LOCAL_DB] Error getting unsynced messages: $e');
      return [];
    }
  }

  // Update message sync status
  Future<void> updateMessageSyncStatus(String messageId, bool isSynced) async {
    await _ensureInitialized();
    try {
      final message = _messagesBox!.get(messageId);
      if (message != null) {
        message.isSynced = isSynced;
        await message.save();
        debugPrint('💾 [LOCAL_DB] Message sync status updated: $messageId -> $isSynced');
      }
    } catch (e) {
      debugPrint('❌ [LOCAL_DB] Error updating message sync status: $e');
    }
  }

  // Delete message
  Future<void> deleteMessage(String messageId) async {
    await _ensureInitialized();
    try {
      await _messagesBox!.delete(messageId);
      debugPrint('💾 [LOCAL_DB] Message deleted: $messageId');
    } catch (e) {
      debugPrint('❌ [LOCAL_DB] Error deleting message: $e');
    }
  }

  // === CHAT ROOM OPERATIONS ===

  // Save chat room locally
  Future<void> saveChatRoom(LocalChatRoom chatRoom) async {
    await _ensureInitialized();
    try {
      await _chatRoomsBox!.put(chatRoom.id, chatRoom);
      debugPrint('💾 [LOCAL_DB] Chat room saved: ${chatRoom.id}');
    } catch (e) {
      debugPrint('❌ [LOCAL_DB] Error saving chat room: $e');
      rethrow;
    }
  }

  // Get all chat rooms
  List<LocalChatRoom> getAllChatRooms() {
    if (!_isInitialized || _chatRoomsBox == null) return [];
    
    try {
      final chatRooms = _chatRoomsBox!.values.toList();
      
      // Sort by last message time (newest first)
      chatRooms.sort((a, b) {
        if (a.lastMessageTime == null && b.lastMessageTime == null) return 0;
        if (a.lastMessageTime == null) return 1;
        if (b.lastMessageTime == null) return -1;
        return b.lastMessageTime!.compareTo(a.lastMessageTime!);
      });
      
      debugPrint('💾 [LOCAL_DB] Retrieved ${chatRooms.length} chat rooms');
      return chatRooms;
    } catch (e) {
      debugPrint('❌ [LOCAL_DB] Error getting chat rooms: $e');
      return [];
    }
  }

  // Get chat room by ID
  LocalChatRoom? getChatRoom(String chatRoomId) {
    if (!_isInitialized || _chatRoomsBox == null) return null;
    
    try {
      return _chatRoomsBox!.get(chatRoomId);
    } catch (e) {
      debugPrint('❌ [LOCAL_DB] Error getting chat room: $e');
      return null;
    }
  }

  // Update chat room
  Future<void> updateChatRoom(LocalChatRoom chatRoom) async {
    await _ensureInitialized();
    try {
      await _chatRoomsBox!.put(chatRoom.id, chatRoom);
      debugPrint('💾 [LOCAL_DB] Chat room updated: ${chatRoom.id}');
    } catch (e) {
      debugPrint('❌ [LOCAL_DB] Error updating chat room: $e');
    }
  }

  // === USER OPERATIONS ===

  // Save user locally
  Future<void> saveUser(LocalUser user) async {
    await _ensureInitialized();
    try {
      await _usersBox!.put(user.uid, user);
      debugPrint('💾 [LOCAL_DB] User saved: ${user.uid}');
    } catch (e) {
      debugPrint('❌ [LOCAL_DB] Error saving user: $e');
      rethrow;
    }
  }

  // Get user by ID
  LocalUser? getUser(String userId) {
    if (!_isInitialized || _usersBox == null) return null;
    
    try {
      return _usersBox!.get(userId);
    } catch (e) {
      debugPrint('❌ [LOCAL_DB] Error getting user: $e');
      return null;
    }
  }

  // Get all users
  List<LocalUser> getAllUsers() {
    if (!_isInitialized || _usersBox == null) return [];
    
    try {
      return _usersBox!.values.toList();
    } catch (e) {
      debugPrint('❌ [LOCAL_DB] Error getting all users: $e');
      return [];
    }
  }

  // === SYNC STATUS OPERATIONS ===

  // Add sync task
  Future<void> addSyncTask(SyncStatus syncStatus) async {
    await _ensureInitialized();
    try {
      await _syncStatusBox!.put(syncStatus.id, syncStatus);
      debugPrint('💾 [LOCAL_DB] Sync task added: ${syncStatus.id}');
    } catch (e) {
      debugPrint('❌ [LOCAL_DB] Error adding sync task: $e');
    }
  }

  // Get pending sync tasks
  List<SyncStatus> getPendingSyncTasks() {
    if (!_isInitialized || _syncStatusBox == null) return [];
    
    try {
      return _syncStatusBox!.values
          .where((task) => !task.isCompleted)
          .toList();
    } catch (e) {
      debugPrint('❌ [LOCAL_DB] Error getting pending sync tasks: $e');
      return [];
    }
  }

  // Update sync task
  Future<void> updateSyncTask(SyncStatus syncStatus) async {
    await _ensureInitialized();
    try {
      await _syncStatusBox!.put(syncStatus.id, syncStatus);
      debugPrint('💾 [LOCAL_DB] Sync task updated: ${syncStatus.id}');
    } catch (e) {
      debugPrint('❌ [LOCAL_DB] Error updating sync task: $e');
    }
  }

  // === UTILITY OPERATIONS ===

  // Clear all data (for logout)
  Future<void> clearAllData() async {
    await _ensureInitialized();
    try {
      await _messagesBox!.clear();
      await _chatRoomsBox!.clear();
      await _usersBox!.clear();
      await _syncStatusBox!.clear();
      debugPrint('💾 [LOCAL_DB] All local data cleared');
    } catch (e) {
      debugPrint('❌ [LOCAL_DB] Error clearing local data: $e');
    }
  }

  // Get database statistics
  Map<String, int> getDatabaseStats() {
    if (!_isInitialized) return {};
    
    return {
      'messages': _messagesBox?.length ?? 0,
      'chatRooms': _chatRoomsBox?.length ?? 0,
      'users': _usersBox?.length ?? 0,
      'syncTasks': _syncStatusBox?.length ?? 0,
    };
  }

  // Close all boxes (for app termination)
  Future<void> close() async {
    try {
      await _messagesBox?.close();
      await _chatRoomsBox?.close();
      await _usersBox?.close();
      await _syncStatusBox?.close();
      _isInitialized = false;
      debugPrint('💾 [LOCAL_DB] Local database closed');
    } catch (e) {
      debugPrint('❌ [LOCAL_DB] Error closing local database: $e');
    }
  }
}
